import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { headers } from 'next/headers'

export const metadata: Metadata = {
  title: 'Timer Kit - Professional Time Management Tools',
  description: 'Free online time management tools: stopwatch, countdown timer, world clock, pomodoro timer and more. Available in 65+ languages.',
  keywords: 'timer, stopwatch, countdown, world clock, pomodoro, time management, online tools',
  openGraph: {
    title: 'Timer Kit - Professional Time Management Tools',
    description: 'Free online time management tools: stopwatch, countdown timer, world clock, pomodoro timer and more. Available in 65+ languages.',
    url: 'https://timerkit.com',
    type: 'website',
  },
  alternates: {
    canonical: 'https://timerkit.com',
    languages: {
      'en': 'https://timerkit.com/en',
      'fr': 'https://timerkit.com/fr',
      'es': 'https://timerkit.com/es',
      'de': 'https://timerkit.com/de',
      'it': 'https://timerkit.com/it',
      'pt': 'https://timerkit.com/pt',
      'nl': 'https://timerkit.com/nl',
      'pl': 'https://timerkit.com/pl',
      'uk': 'https://timerkit.com/uk',
      'tr': 'https://timerkit.com/tr',
      'ru': 'https://timerkit.com/ru',
      'ar': 'https://timerkit.com/ar',
      'he': 'https://timerkit.com/he',
      'fa': 'https://timerkit.com/fa',
      'hi': 'https://timerkit.com/hi',
      'bn': 'https://timerkit.com/bn',
      'te': 'https://timerkit.com/te',
      'ta': 'https://timerkit.com/ta',
      'mr': 'https://timerkit.com/mr',
      'gu': 'https://timerkit.com/gu',
      'kn': 'https://timerkit.com/kn',
      'ml': 'https://timerkit.com/ml',
      'pa': 'https://timerkit.com/pa',
      'ur': 'https://timerkit.com/ur',
      'id': 'https://timerkit.com/id',
      'ms': 'https://timerkit.com/ms',
      'th': 'https://timerkit.com/th',
      'vi': 'https://timerkit.com/vi',
      'km': 'https://timerkit.com/km',
      'my': 'https://timerkit.com/my',
      'zh': 'https://timerkit.com/zh',
      'ja': 'https://timerkit.com/ja',
      'ko': 'https://timerkit.com/ko',
      'el': 'https://timerkit.com/el',
      'bg': 'https://timerkit.com/bg',
      'cs': 'https://timerkit.com/cs',
      'sk': 'https://timerkit.com/sk',
      'hu': 'https://timerkit.com/hu',
      'ro': 'https://timerkit.com/ro',
      'hr': 'https://timerkit.com/hr',
      'sr': 'https://timerkit.com/sr',
      'bs': 'https://timerkit.com/bs',
      'sl': 'https://timerkit.com/sl',
      'mk': 'https://timerkit.com/mk',
      'et': 'https://timerkit.com/et',
      'lv': 'https://timerkit.com/lv',
      'lt': 'https://timerkit.com/lt',
      'da': 'https://timerkit.com/da',
      'fi': 'https://timerkit.com/fi',
      'nb': 'https://timerkit.com/nb',
      'sv': 'https://timerkit.com/sv',
      'ca': 'https://timerkit.com/ca',
      'gl': 'https://timerkit.com/gl',
      'eu': 'https://timerkit.com/eu',
      'af': 'https://timerkit.com/af',
      'sw': 'https://timerkit.com/sw',
      'am': 'https://timerkit.com/am',
      'ka': 'https://timerkit.com/ka',
      'hy': 'https://timerkit.com/hy',
      'az': 'https://timerkit.com/az',
      'uz': 'https://timerkit.com/uz',
      'kk': 'https://timerkit.com/kk',
      'tg': 'https://timerkit.com/tg',
      'tk': 'https://timerkit.com/tk',
      'ky': 'https://timerkit.com/ky',
    }
  },
  robots: {
    index: true,
    follow: true,
  }
}

export default async function RootPage() {
  // Obtenir les headers pour détecter la langue préférée
  const headersList = await headers()
  const userAgent = headersList.get("user-agent") || ""
  const acceptLanguage = headersList.get("accept-language") || ""

  // Vérifier si c'est un bot de moteur de recherche
  const isSearchBot = /googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly|quora link preview|showyoubot|outbrain|pinterest|developers\.google\.com/i.test(userAgent)

  // Pour les bots, rediriger vers la version anglaise pour une indexation cohérente
  if (isSearchBot) {
    redirect('/en')
  }

  // Pour les utilisateurs humains, détecter la langue préférée
  const supportedLanguages = [
    'en', 'fr', 'es', 'de', 'it', 'pt', 'nl', 'pl', 'uk', 'tr', 'ru', 'ar', 'he', 'fa', 'hi', 'bn',
    'te', 'ta', 'mr', 'gu', 'kn', 'ml', 'pa', 'ur', 'id', 'ms', 'th', 'vi', 'km', 'my', 'zh', 'ja',
    'ko', 'el', 'bg', 'cs', 'sk', 'hu', 'ro', 'hr', 'sr', 'bs', 'sl', 'mk', 'et', 'lv', 'lt', 'da',
    'fi', 'nb', 'sv', 'ca', 'gl', 'eu', 'af', 'sw', 'am', 'ka', 'hy', 'az', 'uz', 'kk', 'tg', 'tk', 'ky'
  ]

  const preferredLanguage = acceptLanguage
    .split(",")
    .map((lang) => lang.split(";")[0].trim().substring(0, 2))
    .find((lang) => supportedLanguages.includes(lang)) || "en"

  // Rediriger vers la langue préférée
  redirect(`/${preferredLanguage}`)
}

