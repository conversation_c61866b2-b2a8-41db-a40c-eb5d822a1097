import { NextResponse } from 'next/server'
import { getTranslatedRoute } from '@/lib/i18n/route-translations'

export async function GET() {
  const baseUrl = 'https://timerkit.com'
  
  // Langues supportées
  const languages = [
    'en', 'fr', 'es', 'de', 'it', 'pt', 'nl', 'pl', 'uk', 'tr', 'ru', 'ar', 'he', 'fa', 'hi', 'bn',
    'te', 'ta', 'mr', 'gu', 'kn', 'ml', 'pa', 'ur', 'id', 'ms', 'th', 'vi', 'km', 'my', 'zh', 'ja',
    'ko', 'el', 'bg', 'cs', 'sk', 'hu', 'ro', 'hr', 'sr', 'bs', 'sl', 'mk', 'et', 'lv', 'lt', 'da',
    'fi', 'nb', 'sv', 'ca', 'gl', 'eu', 'af', 'sw', 'am', 'ka', 'hy', 'az', 'uz', 'kk', 'tg', 'tk', 'ky'
  ]
  
  // Routes disponibles
  const routes = [
    'timer', 'countdown', 'todo', 'time-tracking', 'world-clock',
    'intervals', 'pomodoro', 'meeting-timer', 'time-billing',
    'workout-intervals', 'exercise-templates', 'sitemap'
  ]
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
  <!-- Page racine -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>`
  
  // Ajouter les liens hreflang pour la page racine
  for (const lang of languages) {
    sitemap += `
    <xhtml:link rel="alternate" hreflang="${lang}" href="${baseUrl}/${lang}"/>`
  }
  
  sitemap += `
  </url>
  
  <!-- Pages d'accueil par langue -->
`
  
  for (const lang of languages) {
    sitemap += `  <url>
    <loc>${baseUrl}/${lang}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>`
    
    // Ajouter les liens hreflang pour chaque page d'accueil
    for (const altLang of languages) {
      sitemap += `
    <xhtml:link rel="alternate" hreflang="${altLang}" href="${baseUrl}/${altLang}"/>`
    }
    
    sitemap += `
  </url>
`
  }
  
  // Pages d'outils par langue
  sitemap += `  <!-- Pages d'outils -->
`
  
  for (const route of routes) {
    for (const lang of languages) {
      const translatedRoute = getTranslatedRoute(lang, route)
      sitemap += `  <url>
    <loc>${baseUrl}/${lang}/${translatedRoute}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>`
      
      // Ajouter les liens hreflang pour chaque page d'outil
      for (const altLang of languages) {
        const altTranslatedRoute = getTranslatedRoute(altLang, route)
        sitemap += `
    <xhtml:link rel="alternate" hreflang="${altLang}" href="${baseUrl}/${altLang}/${altTranslatedRoute}"/>`
      }
      
      sitemap += `
  </url>
`
    }
  }
  
  // Pages légales (uniquement en anglais)
  sitemap += `  <!-- Pages légales -->
  <url>
    <loc>${baseUrl}/terms</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
  <url>
    <loc>${baseUrl}/privacy-policy</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.3</priority>
  </url>
</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600'
    }
  })
}
