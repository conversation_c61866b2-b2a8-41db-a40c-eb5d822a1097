import { NextResponse } from 'next/server'

export async function GET() {
  const robotsTxt = `User-agent: *
Allow: /

# Sitemap
Sitemap: https://timerkit.com/sitemap.xml

# Optimisations pour les moteurs de recherche
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# Bloquer les fichiers non nécessaires
Disallow: /_next/
Disallow: /api/
Disallow: *.json$
Disallow: *.js$
Disallow: *.css$
Disallow: *.map$

# Permettre l'accès aux icônes et images
Allow: /icons/
Allow: /images/
Allow: /sounds/
Allow: /*.png$
Allow: /*.jpg$
Allow: /*.jpeg$
Allow: /*.gif$
Allow: /*.svg$
Allow: /*.ico$
Allow: /*.webp$
Allow: /*.mp3$
Allow: /*.wav$
Allow: /*.ogg$`

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400'
    }
  })
}
